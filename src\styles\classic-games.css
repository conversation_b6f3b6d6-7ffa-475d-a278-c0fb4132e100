.classic-games-container {
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-light);
  font-family: "<PERSON><PERSON>", sans-serif;
}

.dark-mode .page-title {
  color: var(--text-dark);
}

.page-subtitle {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  color: #666;
  font-weight: 400;
}

.dark-mode .page-subtitle {
  color: #aaa;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.game-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.dark-mode .game-card {
  background: #2d2d2d;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark-mode .game-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.game-card.coming-soon {
  opacity: 0.7;
  cursor: not-allowed;
}

.game-card.coming-soon:hover {
  transform: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dark-mode .game-card.coming-soon:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.game-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.game-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.dark-mode .game-card h3 {
  color: var(--text-dark);
}

.game-card p {
  font-size: 1rem;
  line-height: 1.5;
  color: #666;
  margin-bottom: 1rem;
}

.dark-mode .game-card p {
  color: #aaa;
}

.coming-soon-badge {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
  margin-top: 0.5rem;
}

.play-button {
  background: linear-gradient(135deg, var(--text-light) 0%, #1565c0 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  font-family: "Josefin Sans", sans-serif;
}

.play-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3);
}

.dark-mode .play-button {
  background: linear-gradient(135deg, var(--text-dark) 0%, #90caf9 100%);
  color: var(--bg-dark);
}

.dark-mode .play-button:hover {
  box-shadow: 0 4px 12px rgba(227, 242, 253, 0.3);
}

.development-notice {
  background: rgba(13, 71, 161, 0.05);
  border: 2px solid rgba(13, 71, 161, 0.1);
  border-radius: 15px;
  padding: 2rem;
  margin-top: 2rem;
  text-align: center;
}

.dark-mode .development-notice {
  background: rgba(227, 242, 253, 0.05);
  border-color: rgba(227, 242, 253, 0.1);
}

.development-notice h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.dark-mode .development-notice h2 {
  color: var(--text-dark);
}

.development-notice p {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  margin-bottom: 1rem;
}

.dark-mode .development-notice p {
  color: #aaa;
}

.development-notice p:last-child {
  margin-bottom: 0;
}

.inline-link {
  background: none;
  border: none;
  color: var(--text-light);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0;
  transition: opacity 0.2s ease;
}

.inline-link:hover {
  opacity: 0.8;
}

.dark-mode .inline-link {
  color: var(--text-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .classic-games-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .page-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .games-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .game-card {
    padding: 1.5rem;
  }

  .game-icon {
    font-size: 2.5rem;
  }

  .game-card h3 {
    font-size: 1.3rem;
  }

  .development-notice {
    padding: 1.5rem;
  }

  .development-notice h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 2rem;
  }

  .game-card {
    padding: 1rem;
  }

  .game-icon {
    font-size: 2rem;
  }

  .game-card h3 {
    font-size: 1.2rem;
  }

  .development-notice {
    padding: 1rem;
  }

  .development-notice h2 {
    font-size: 1.3rem;
  }
}
