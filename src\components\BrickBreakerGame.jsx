import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Stage, Layer, Rect, Circle, Text } from 'react-konva';

const BrickBreakerGame = () => {
  const stageRef = useRef();
  const animationRef = useRef();
  const [isMobile, setIsMobile] = useState(false);

  // Responsive game dimensions
  const getGameDimensions = () => {
    const isMobileDevice = window.innerWidth <= 768;
    setIsMobile(isMobileDevice);

    if (isMobileDevice) {
      const maxWidth = Math.min(window.innerWidth - 40, 400);
      return {
        width: maxWidth,
        height: (maxWidth * 0.75) // 4:3 aspect ratio for mobile
      };
    }
    return { width: 800, height: 600 };
  };

  const [gameDimensions, setGameDimensions] = useState(getGameDimensions());
  const GAME_WIDTH = gameDimensions.width;
  const GAME_HEIGHT = gameDimensions.height;
  // Responsive game constants
  const PADDLE_WIDTH = isMobile ? Math.max(60, GAME_WIDTH * 0.15) : 100;
  const PADDLE_HEIGHT = isMobile ? 12 : 15;
  const BALL_RADIUS = isMobile ? 6 : 8;
  const BRICK_COLS = isMobile ? 6 : 10;
  const BRICK_WIDTH = (GAME_WIDTH - (BRICK_COLS + 1) * 5) / BRICK_COLS;
  const BRICK_HEIGHT = isMobile ? 15 : 20;
  const BRICK_ROWS = 6;
  const BRICK_PADDING = 5;

  // Game state
  const [gameState, setGameState] = useState('waiting'); // 'waiting', 'playing', 'paused', 'gameOver', 'won', 'levelComplete'
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  const [level, setLevel] = useState(1);
  const [ballSpeed, setBallSpeed] = useState(4);

  // Game objects
  const [paddle, setPaddle] = useState({
    x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
    y: GAME_HEIGHT - 50,
    width: PADDLE_WIDTH,
    height: PADDLE_HEIGHT
  });

  const [ball, setBall] = useState({
    x: GAME_WIDTH / 2,
    y: GAME_HEIGHT - 70,
    radius: BALL_RADIUS,
    dx: 0,
    dy: 0
  });

  const [bricks, setBricks] = useState([]);

  // Level configurations
  const levelConfigs = {
    1: {
      rows: 4,
      pattern: 'full',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
      ballSpeed: 4,
      pointsPerBrick: 10
    },
    2: {
      rows: 5,
      pattern: 'checkerboard',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
      ballSpeed: 4.5,
      pointsPerBrick: 15
    },
    3: {
      rows: 6,
      pattern: 'pyramid',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
      ballSpeed: 5,
      pointsPerBrick: 20
    },
    4: {
      rows: 6,
      pattern: 'diamond',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
      ballSpeed: 5.5,
      pointsPerBrick: 25
    },
    5: {
      rows: 7,
      pattern: 'full',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FFB6C1'],
      ballSpeed: 6,
      pointsPerBrick: 30
    }
  };

  // Initialize bricks based on level
  const initializeBricks = useCallback(() => {
    const config = levelConfigs[level] || levelConfigs[5]; // Default to level 5 for higher levels
    const newBricks = [];

    for (let row = 0; row < config.rows; row++) {
      for (let col = 0; col < BRICK_COLS; col++) {
        let shouldCreateBrick = true;

        // Apply pattern logic
        switch (config.pattern) {
          case 'checkerboard':
            shouldCreateBrick = (row + col) % 2 === 0;
            break;
          case 'pyramid':
            const centerCol = BRICK_COLS / 2;
            const maxDistance = Math.min(row + 1, BRICK_COLS / 2);
            shouldCreateBrick = Math.abs(col - centerCol + 0.5) < maxDistance;
            break;
          case 'diamond':
            const center = BRICK_COLS / 2;
            const diamondSize = Math.min(3, config.rows - Math.abs(row - config.rows / 2));
            shouldCreateBrick = Math.abs(col - center + 0.5) < diamondSize;
            break;
          case 'full':
          default:
            shouldCreateBrick = true;
            break;
        }

        if (shouldCreateBrick) {
          newBricks.push({
            x: col * (BRICK_WIDTH + BRICK_PADDING) + BRICK_PADDING,
            y: row * (BRICK_HEIGHT + BRICK_PADDING) + 50,
            width: BRICK_WIDTH,
            height: BRICK_HEIGHT,
            color: config.colors[row % config.colors.length],
            destroyed: false,
            id: `${row}-${col}`,
            points: config.pointsPerBrick
          });
        }
      }
    }
    setBricks(newBricks);
    setBallSpeed(config.ballSpeed);
  }, [level]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const newDimensions = getGameDimensions();
      setGameDimensions(newDimensions);
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial call

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize game
  useEffect(() => {
    initializeBricks();
  }, [initializeBricks]);

  // Start game
  const startGame = () => {
    const angle = (Math.random() - 0.5) * 0.8; // Random angle
    setBall(prev => ({
      ...prev,
      dx: ballSpeed * Math.sin(angle),
      dy: -ballSpeed * Math.cos(angle)
    }));
    setGameState('playing');
  };

  // Next level
  const nextLevel = () => {
    setLevel(prev => prev + 1);
    setPaddle({
      x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
      y: GAME_HEIGHT - 50,
      width: PADDLE_WIDTH,
      height: PADDLE_HEIGHT
    });
    setBall({
      x: GAME_WIDTH / 2,
      y: GAME_HEIGHT - 70,
      radius: BALL_RADIUS,
      dx: 0,
      dy: 0
    });
    setGameState('waiting');
  };

  // Reset game
  const resetGame = () => {
    setPaddle({
      x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
      y: GAME_HEIGHT - 50,
      width: PADDLE_WIDTH,
      height: PADDLE_HEIGHT
    });
    setBall({
      x: GAME_WIDTH / 2,
      y: GAME_HEIGHT - 70,
      radius: BALL_RADIUS,
      dx: 0,
      dy: 0
    });
    setScore(0);
    setLives(3);
    setLevel(1);
    setBallSpeed(4);
    setGameState('waiting');
  };

  // Collision detection
  const checkCollision = (rect1, rect2) => {
    return rect1.x < rect2.x + rect2.width &&
      rect1.x + rect1.width > rect2.x &&
      rect1.y < rect2.y + rect2.height &&
      rect1.y + rect1.height > rect2.y;
  };

  // Ball collision with circle
  const checkBallCollision = (ball, rect) => {
    const distX = Math.abs(ball.x - rect.x - rect.width / 2);
    const distY = Math.abs(ball.y - rect.y - rect.height / 2);

    if (distX > (rect.width / 2 + ball.radius)) return false;
    if (distY > (rect.height / 2 + ball.radius)) return false;

    if (distX <= (rect.width / 2)) return true;
    if (distY <= (rect.height / 2)) return true;

    const dx = distX - rect.width / 2;
    const dy = distY - rect.height / 2;
    return (dx * dx + dy * dy <= (ball.radius * ball.radius));
  };

  // Game loop
  const gameLoop = useCallback(() => {
    if (gameState !== 'playing') return;

    setBall(prevBall => {
      let newBall = { ...prevBall };
      newBall.x += newBall.dx;
      newBall.y += newBall.dy;

      // Wall collisions
      if (newBall.x <= newBall.radius || newBall.x >= GAME_WIDTH - newBall.radius) {
        newBall.dx = -newBall.dx;
      }
      if (newBall.y <= newBall.radius) {
        newBall.dy = -newBall.dy;
      }

      // Bottom wall (lose life)
      if (newBall.y >= GAME_HEIGHT - newBall.radius) {
        setLives(prev => {
          const newLives = prev - 1;
          if (newLives <= 0) {
            setGameState('gameOver');
          } else {
            // Reset ball position
            newBall = {
              x: GAME_WIDTH / 2,
              y: GAME_HEIGHT - 70,
              radius: BALL_RADIUS,
              dx: 0,
              dy: 0
            };
            setGameState('waiting');
          }
          return newLives;
        });
      }

      // Paddle collision
      if (checkBallCollision(newBall, paddle)) {
        const paddleCenter = paddle.x + paddle.width / 2;
        const ballRelativePos = (newBall.x - paddleCenter) / (paddle.width / 2);
        newBall.dx = ballRelativePos * 5;
        newBall.dy = -Math.abs(newBall.dy);
      }

      // Brick collisions
      setBricks(prevBricks => {
        const newBricks = [...prevBricks];
        let brickHit = false;

        for (let i = 0; i < newBricks.length; i++) {
          if (!newBricks[i].destroyed && checkBallCollision(newBall, newBricks[i])) {
            newBricks[i].destroyed = true;
            newBall.dy = -newBall.dy;
            brickHit = true;
            setScore(prev => prev + (newBricks[i].points || 10));
            break;
          }
        }

        // Check level completion
        const remainingBricks = newBricks.filter(brick => !brick.destroyed);
        if (remainingBricks.length === 0) {
          if (level >= 5) {
            setGameState('won'); // Game completed
          } else {
            setGameState('levelComplete');
          }
        }

        return newBricks;
      });

      return newBall;
    });
  }, [gameState, paddle]);

  // Animation loop
  useEffect(() => {
    const animate = () => {
      gameLoop();
      animationRef.current = requestAnimationFrame(animate);
    };

    if (gameState === 'playing') {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameLoop, gameState]);

  // Mouse/Touch movement for paddle
  const handlePointerMove = (e) => {
    if (gameState === 'playing' || gameState === 'waiting') {
      const stage = stageRef.current;
      const pointerPos = stage.getPointerPosition();
      if (pointerPos) {
        const newX = Math.max(0, Math.min(GAME_WIDTH - PADDLE_WIDTH, pointerPos.x - PADDLE_WIDTH / 2));
        setPaddle(prev => ({ ...prev, x: newX }));

        // Move ball with paddle when waiting
        if (gameState === 'waiting') {
          setBall(prev => ({ ...prev, x: newX + PADDLE_WIDTH / 2 }));
        }
      }
    }
  };

  // Touch-specific handlers for mobile
  const handleTouchMove = (e) => {
    e.evt.preventDefault(); // Prevent scrolling
    handlePointerMove(e);
  };

  // Click to start
  const handleClick = () => {
    if (gameState === 'waiting') {
      startGame();
    } else if (gameState === 'levelComplete') {
      nextLevel();
    } else if (gameState === 'gameOver' || gameState === 'won') {
      resetGame();
    }
  };

  return (
    <div style={{ textAlign: 'center', padding: isMobile ? '10px' : '20px' }}>
      <div style={{
        marginBottom: '10px',
        fontSize: isMobile ? '14px' : '16px',
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: isMobile ? '10px' : '20px'
      }}>
        <span>Score: {score}</span>
        <span>Lives: {lives}</span>
        <span>Level: {level}</span>
      </div>

      <div style={{
        marginBottom: '10px',
        fontSize: isMobile ? '12px' : '14px',
        minHeight: isMobile ? '20px' : '24px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        {gameState === 'waiting' && <span>{isMobile ? 'Tap' : 'Click'} to start Level {level}!</span>}
        {gameState === 'levelComplete' && <span style={{ color: 'green' }}>Level {level} Complete! {isMobile ? 'Tap' : 'Click'} for Level {level + 1}</span>}
        {gameState === 'gameOver' && <span style={{ color: 'red' }}>Game Over! {isMobile ? 'Tap' : 'Click'} to restart</span>}
        {gameState === 'won' && <span style={{ color: 'gold' }}>🎉 All Levels Complete! You're a Brick Breaker Master! {isMobile ? 'Tap' : 'Click'} to play again</span>}
      </div>

      <Stage
        width={GAME_WIDTH}
        height={GAME_HEIGHT}
        ref={stageRef}
        onMouseMove={handlePointerMove}
        onTouchMove={handleTouchMove}
        onTouchStart={handlePointerMove}
        onClick={handleClick}
        onTap={handleClick}
        style={{
          border: '2px solid #333',
          cursor: isMobile ? 'default' : 'none',
          touchAction: 'none',
          userSelect: 'none'
        }}
      >
        <Layer>
          {/* Background */}
          <Rect width={GAME_WIDTH} height={GAME_HEIGHT} fill="#1a1a2e" />

          {/* Bricks */}
          {bricks.map(brick => (
            !brick.destroyed && (
              <Rect
                key={brick.id}
                x={brick.x}
                y={brick.y}
                width={brick.width}
                height={brick.height}
                fill={brick.color}
                stroke="#fff"
                strokeWidth={1}
                cornerRadius={3}
              />
            )
          ))}

          {/* Paddle */}
          <Rect
            x={paddle.x}
            y={paddle.y}
            width={paddle.width}
            height={paddle.height}
            fill="#fff"
            cornerRadius={7}
          />

          {/* Ball */}
          <Circle
            x={ball.x}
            y={ball.y}
            radius={ball.radius}
            fill="#fff"
          />

          {/* Instructions */}
          {gameState === 'waiting' && (
            <Text
              x={GAME_WIDTH / 2}
              y={GAME_HEIGHT / 2}
              text={`Level ${level}\n${isMobile ? 'Touch screen to control paddle' : 'Move mouse to control paddle'}\n${isMobile ? 'Tap' : 'Click'} to launch ball`}
              fontSize={isMobile ? 16 : 20}
              fill="#fff"
              align="center"
              offsetX={isMobile ? 80 : 100}
            />
          )}

          {/* Level Complete Message */}
          {gameState === 'levelComplete' && (
            <Text
              x={GAME_WIDTH / 2}
              y={GAME_HEIGHT / 2}
              text={`🎉 Level ${level} Complete! 🎉\n${isMobile ? 'Tap' : 'Click'} to continue to Level ${level + 1}`}
              fontSize={isMobile ? 18 : 24}
              fill="#FFD700"
              align="center"
              offsetX={isMobile ? 100 : 150}
            />
          )}
        </Layer>
      </Stage>
    </div>
  );
};

export default BrickBreakerGame;
