import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Stage, Layer, Rect, Circle, Text } from 'react-konva';

const BrickBreakerGame = () => {
  const stageRef = useRef();
  const animationRef = useRef();
  
  // Game dimensions
  const GAME_WIDTH = 800;
  const GAME_HEIGHT = 600;
  const PADDLE_WIDTH = 100;
  const PADDLE_HEIGHT = 15;
  const BALL_RADIUS = 8;
  const BRICK_WIDTH = 75;
  const BRICK_HEIGHT = 20;
  const BRICK_ROWS = 6;
  const BRICK_COLS = 10;
  const BRICK_PADDING = 5;

  // Game state
  const [gameState, setGameState] = useState('waiting'); // 'waiting', 'playing', 'paused', 'gameOver', 'won'
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  
  // Game objects
  const [paddle, setPaddle] = useState({
    x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
    y: GAME_HEIGHT - 50,
    width: PADDLE_WIDTH,
    height: PADDLE_HEIGHT
  });

  const [ball, setBall] = useState({
    x: GAME_WIDTH / 2,
    y: GAME_HEIGHT - 70,
    radius: BALL_RADIUS,
    dx: 0,
    dy: 0
  });

  const [bricks, setBricks] = useState([]);

  // Initialize bricks
  const initializeBricks = useCallback(() => {
    const newBricks = [];
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    
    for (let row = 0; row < BRICK_ROWS; row++) {
      for (let col = 0; col < BRICK_COLS; col++) {
        newBricks.push({
          x: col * (BRICK_WIDTH + BRICK_PADDING) + BRICK_PADDING,
          y: row * (BRICK_HEIGHT + BRICK_PADDING) + 50,
          width: BRICK_WIDTH,
          height: BRICK_HEIGHT,
          color: colors[row],
          destroyed: false,
          id: `${row}-${col}`
        });
      }
    }
    setBricks(newBricks);
  }, []);

  // Initialize game
  useEffect(() => {
    initializeBricks();
  }, [initializeBricks]);

  // Start game
  const startGame = () => {
    setBall(prev => ({
      ...prev,
      dx: 3 + Math.random() * 2,
      dy: -4
    }));
    setGameState('playing');
  };

  // Reset game
  const resetGame = () => {
    setPaddle({
      x: GAME_WIDTH / 2 - PADDLE_WIDTH / 2,
      y: GAME_HEIGHT - 50,
      width: PADDLE_WIDTH,
      height: PADDLE_HEIGHT
    });
    setBall({
      x: GAME_WIDTH / 2,
      y: GAME_HEIGHT - 70,
      radius: BALL_RADIUS,
      dx: 0,
      dy: 0
    });
    setScore(0);
    setLives(3);
    setGameState('waiting');
    initializeBricks();
  };

  // Collision detection
  const checkCollision = (rect1, rect2) => {
    return rect1.x < rect2.x + rect2.width &&
           rect1.x + rect1.width > rect2.x &&
           rect1.y < rect2.y + rect2.height &&
           rect1.y + rect1.height > rect2.y;
  };

  // Ball collision with circle
  const checkBallCollision = (ball, rect) => {
    const distX = Math.abs(ball.x - rect.x - rect.width / 2);
    const distY = Math.abs(ball.y - rect.y - rect.height / 2);

    if (distX > (rect.width / 2 + ball.radius)) return false;
    if (distY > (rect.height / 2 + ball.radius)) return false;

    if (distX <= (rect.width / 2)) return true;
    if (distY <= (rect.height / 2)) return true;

    const dx = distX - rect.width / 2;
    const dy = distY - rect.height / 2;
    return (dx * dx + dy * dy <= (ball.radius * ball.radius));
  };

  // Game loop
  const gameLoop = useCallback(() => {
    if (gameState !== 'playing') return;

    setBall(prevBall => {
      let newBall = { ...prevBall };
      newBall.x += newBall.dx;
      newBall.y += newBall.dy;

      // Wall collisions
      if (newBall.x <= newBall.radius || newBall.x >= GAME_WIDTH - newBall.radius) {
        newBall.dx = -newBall.dx;
      }
      if (newBall.y <= newBall.radius) {
        newBall.dy = -newBall.dy;
      }

      // Bottom wall (lose life)
      if (newBall.y >= GAME_HEIGHT - newBall.radius) {
        setLives(prev => {
          const newLives = prev - 1;
          if (newLives <= 0) {
            setGameState('gameOver');
          } else {
            // Reset ball position
            newBall = {
              x: GAME_WIDTH / 2,
              y: GAME_HEIGHT - 70,
              radius: BALL_RADIUS,
              dx: 0,
              dy: 0
            };
            setGameState('waiting');
          }
          return newLives;
        });
      }

      // Paddle collision
      if (checkBallCollision(newBall, paddle)) {
        const paddleCenter = paddle.x + paddle.width / 2;
        const ballRelativePos = (newBall.x - paddleCenter) / (paddle.width / 2);
        newBall.dx = ballRelativePos * 5;
        newBall.dy = -Math.abs(newBall.dy);
      }

      // Brick collisions
      setBricks(prevBricks => {
        const newBricks = [...prevBricks];
        let brickHit = false;

        for (let i = 0; i < newBricks.length; i++) {
          if (!newBricks[i].destroyed && checkBallCollision(newBall, newBricks[i])) {
            newBricks[i].destroyed = true;
            newBall.dy = -newBall.dy;
            brickHit = true;
            setScore(prev => prev + 10);
            break;
          }
        }

        // Check win condition
        const remainingBricks = newBricks.filter(brick => !brick.destroyed);
        if (remainingBricks.length === 0) {
          setGameState('won');
        }

        return newBricks;
      });

      return newBall;
    });
  }, [gameState, paddle]);

  // Animation loop
  useEffect(() => {
    const animate = () => {
      gameLoop();
      animationRef.current = requestAnimationFrame(animate);
    };

    if (gameState === 'playing') {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameLoop, gameState]);

  // Mouse movement for paddle
  const handleMouseMove = (e) => {
    if (gameState === 'playing' || gameState === 'waiting') {
      const stage = stageRef.current;
      const pointerPos = stage.getPointerPosition();
      const newX = Math.max(0, Math.min(GAME_WIDTH - PADDLE_WIDTH, pointerPos.x - PADDLE_WIDTH / 2));
      setPaddle(prev => ({ ...prev, x: newX }));
      
      // Move ball with paddle when waiting
      if (gameState === 'waiting') {
        setBall(prev => ({ ...prev, x: newX + PADDLE_WIDTH / 2 }));
      }
    }
  };

  // Click to start
  const handleClick = () => {
    if (gameState === 'waiting') {
      startGame();
    } else if (gameState === 'gameOver' || gameState === 'won') {
      resetGame();
    }
  };

  return (
    <div style={{ textAlign: 'center', padding: '20px' }}>
      <div style={{ marginBottom: '10px' }}>
        <span style={{ marginRight: '20px' }}>Score: {score}</span>
        <span style={{ marginRight: '20px' }}>Lives: {lives}</span>
        {gameState === 'waiting' && <span>Click to start!</span>}
        {gameState === 'gameOver' && <span style={{ color: 'red' }}>Game Over! Click to restart</span>}
        {gameState === 'won' && <span style={{ color: 'green' }}>You Won! Click to play again</span>}
      </div>
      
      <Stage
        width={GAME_WIDTH}
        height={GAME_HEIGHT}
        ref={stageRef}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
        style={{ border: '2px solid #333', cursor: 'none' }}
      >
        <Layer>
          {/* Background */}
          <Rect width={GAME_WIDTH} height={GAME_HEIGHT} fill="#1a1a2e" />
          
          {/* Bricks */}
          {bricks.map(brick => (
            !brick.destroyed && (
              <Rect
                key={brick.id}
                x={brick.x}
                y={brick.y}
                width={brick.width}
                height={brick.height}
                fill={brick.color}
                stroke="#fff"
                strokeWidth={1}
                cornerRadius={3}
              />
            )
          ))}
          
          {/* Paddle */}
          <Rect
            x={paddle.x}
            y={paddle.y}
            width={paddle.width}
            height={paddle.height}
            fill="#fff"
            cornerRadius={7}
          />
          
          {/* Ball */}
          <Circle
            x={ball.x}
            y={ball.y}
            radius={ball.radius}
            fill="#fff"
          />
          
          {/* Instructions */}
          {gameState === 'waiting' && (
            <Text
              x={GAME_WIDTH / 2}
              y={GAME_HEIGHT / 2}
              text="Move mouse to control paddle\nClick to launch ball"
              fontSize={20}
              fill="#fff"
              align="center"
              offsetX={100}
            />
          )}
        </Layer>
      </Stage>
    </div>
  );
};

export default BrickBreakerGame;
