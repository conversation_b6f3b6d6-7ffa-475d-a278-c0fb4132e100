import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import BrickBreakerGame from '../components/BrickBreakerGame';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import '../styles/app.css';
import '../styles/game-page.css';

const BrickBreakerPage = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const handleNavigate = (page) => {
    navigate(`/${page}`);
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>

      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div className="game-page-container">
        <div className="game-header">
          <button
            className="back-button"
            onClick={() => navigate('/classic-games')}
          >
            <ArrowBackIcon /> Back to Games
          </button>
          <h1 className="game-title">🧱 Brick Breaker</h1>
          <p className="game-description">
            Break all the bricks with your paddle and ball! Progress through 5 challenging levels
            with different patterns and increasing difficulty. Use your mouse or touch to control the paddle
            and click/tap to launch the ball. Don't let the ball fall off the bottom!
          </p>
        </div>

        <div className="game-container">
          <BrickBreakerGame />
        </div>

        <div className="game-instructions">
          <h3>How to Play:</h3>
          <ul>
            <li>🖱️📱 Move mouse or touch screen to control the paddle</li>
            <li>🖱️📱 Click or tap to launch the ball</li>
            <li>🧱 Break all bricks to complete each level</li>
            <li>📈 Progress through 5 levels with increasing difficulty</li>
            <li>❤️ You have 3 lives - don't let the ball fall!</li>
            <li>🎯 Score more points in higher levels</li>
            <li>🏆 Complete all 5 levels to become a Brick Breaker Master!</li>
          </ul>
        </div>
      </div>

      <Footer onNavigate={handleNavigate} />
      <CookieConsent />
    </div>
  );
};

export default BrickBreakerPage;
