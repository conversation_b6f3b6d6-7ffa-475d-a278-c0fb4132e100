.game-page-container {
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
  text-align: center;
}

.game-header {
  margin-bottom: 2rem;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--text-light);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
  font-family: "Josefin Sans", sans-serif;
}

.back-button:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3);
}

.dark-mode .back-button {
  background: var(--text-dark);
  color: var(--bg-dark);
}

.dark-mode .back-button:hover {
  background: #90caf9;
  box-shadow: 0 4px 12px rgba(227, 242, 253, 0.3);
}

.game-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-light);
  font-family: "<PERSON><PERSON> Sans", sans-serif;
}

.dark-mode .game-title {
  color: var(--text-dark);
}

.game-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #666;
  font-weight: 400;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.dark-mode .game-description {
  color: #aaa;
}

.game-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  overflow-x: auto;
  padding: 0 1rem;
  touch-action: manipulation;
}

.game-container canvas {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.game-instructions {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
  text-align: left;
}

.dark-mode .game-instructions {
  background: #2d2d2d;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.game-instructions h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-light);
  text-align: center;
}

.dark-mode .game-instructions h3 {
  color: var(--text-dark);
}

.game-instructions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.game-instructions li {
  padding: 0.5rem 0;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.dark-mode .game-instructions li {
  color: #aaa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-page-container {
    padding: 1rem 0.5rem;
  }

  .game-title {
    font-size: 2.5rem;
  }

  .game-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
  }

  .game-container {
    overflow-x: auto;
    padding: 0;
    margin-bottom: 1.5rem;
  }

  .game-instructions {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .game-instructions h3 {
    font-size: 1.3rem;
  }

  .game-instructions li {
    font-size: 0.9rem;
  }

  .back-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .game-page-container {
    padding: 0.5rem 0.25rem;
  }

  .game-title {
    font-size: 2rem;
  }

  .game-description {
    font-size: 0.9rem;
    padding: 0 0.5rem;
  }

  .game-container {
    padding: 0;
    margin-bottom: 1rem;
  }

  .game-instructions {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .game-instructions h3 {
    font-size: 1.2rem;
  }

  .game-instructions li {
    font-size: 0.85rem;
    padding: 0.4rem 0;
  }

  .back-button {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}